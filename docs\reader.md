## 📋 文档概述

本指南专门为与Augment Code AI助手协作开发电子书阅读器Yu Reader而设计，采用**纯桌面端开发模式**（Electron + Vue3 + Element Plus + SQLite3），提供智能化、高效的协作开发流程。利用Augment Code的强大代码理解和生成能力，实现快速、准确的功能开发和问题解决。

### 🎯 项目目标

开发一个现代化的跨平台桌面电子书阅读器应用，基于Electron + Vue3 + Element Plus + SQLite3技术栈，支持Windows、macOS、Linux三大平台，包含5大核心功能模块：

#### 📊 核心功能模块详细分析

**1. 我的书架模块 (Bookshelf)**
- **图书列表**：本地图书库展示、元数据管理、分类组织、标签系统
- **导入图书**：多格式支持（EPUB、PDF、TXT、MOBI、AZW3）、批量处理、自动识别、本地存储

**2. 学习服务模块 (Learning)**
- **作业任务**：作业创建、提交、批改、成绩管理
- **测试任务**：在线测试、题库管理、成绩统计、错题分析
- **阅读任务**：阅读计划、进度跟踪、目标设定、完成记录
- **实验任务**：实验设计、数据收集、结果分析、报告生成

**3. 书签笔记模块 (Bookmark)**
- **书签管理**：书签数据与图书ID关联存储、分类和标签管理、基于数据库索引的快速检索和跳转
- **笔记管理**：高亮标注、文本笔记、富文本编辑、笔记分类和标签
- **数据关联**：书签笔记与图书内容的精确位置绑定、跨格式位置映射、智能内容识别

**4. 设置模块 (Settings)**
- **通用设置**：界面偏好、语言选择、主题切换、快捷键配置
- **阅读设置**：字体调节、护眼模式、阅读模式、页面布局

**5. 个人中心模块 (Profile)**
- **图书学习报告**：阅读统计、学习进度、成绩分析、学习轨迹
- **账号设置**：用户信息、密码修改、偏好设置、数据管理
- **网络设置**：同步配置、云端备份、网络代理、更新管理