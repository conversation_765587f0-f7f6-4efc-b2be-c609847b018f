一份项目简介（Project Brief）。这份简介旨在清晰地定义项目的核心目标、技术栈、核心功能模块和开发模式，为后续的详细规划和开发工作奠定基础。

---

### **项目简介 (Project Brief)**

**项目名称：** Yu Reader (玉阅读器)

**项目概述：**
Yu Reader 是一个现代化的跨平台桌面电子书阅读器应用。它旨在为用户提供一个纯净、高效的阅读体验，并集成多种学习和管理功能。该应用将采用纯桌面端开发模式，利用现代技术栈实现跨平台支持，首先聚焦于 Windows 和 macOS 平台。

**项目目标：**
开发一个功能全面的桌面电子书阅读器，具备以下核心特性：
* **跨平台支持：** 兼容 Windows 和 macOS 操作系统。
* **现代化技术栈：** 利用 Electron, Vue3, Element Plus 和 SQLite3 构建，确保性能和可维护性。
* **核心功能模块：** 包含书架管理、学习服务、书签笔记、设置和个人中心等五大核心功能，满足用户多维度需求。

**技术栈：**
* **前端框架：** Vue3
* **UI 组件库：** Element Plus
* **跨平台框架：** Electron
* **数据库：** SQLite3

**核心功能模块：**

**1. 我的书架模块 (Bookshelf)**
* **功能描述：** 管理用户的电子书库。
* **主要功能点：** 书籍导入、分类、排序、封面展示、阅读进度同步等。

**2. 学习服务模块 (Learning)**
* **功能描述：** 增强用户的学习体验。
* **主要功能点：** 划词翻译、生词本、阅读统计、学习计划等。

**3. 书签笔记模块 (Bookmark)**
* **功能描述：** 方便用户记录和整理阅读中的灵感。
* **主要功能点：** 添加书签、划线高亮、笔记记录、笔记导出等。

**4. 设置模块 (Settings)**
* **功能描述：** 个性化用户的阅读环境和应用设置。
* **主要功能点：** 阅读字体、背景颜色、排版设置、快捷键设置、账号管理等。

**5. 个人中心模块 (Profile)**
* **功能描述：** 展示和管理用户的个人信息及数据。
* **主要功能点：** 用户登录/注册、阅读时长统计、成就系统、数据同步等。

**开发模式：**
本项目将利用 Augment Code AI 助手进行协作开发，采用其强大的代码理解和生成能力，实现高效、智能的开发流程。


根据您提供的项目目标和我的搜索结果，我整理了以下分析：

### **市场趋势和用户期望**
* **多格式支持：** 用户期望阅读器能够支持多种主流电子书格式，如 EPUB、MOBI、PDF、DOCX、TXT、FB2 等。
* **个性化阅读设置：** 高度可定制的阅读体验是关键。这包括但不限于：
    * 调整字体大小、字体类型、行间距、页边距。
    * 提供多种阅读模式（例如：单页、双页、连续滚动）。
    * 自定义主题（例如：默认、棕褐色、深色模式）。
* **护眼功能：** 虽然桌面应用不具备 E-Ink 屏幕，但用户仍然非常关注阅读对视力的影响。提供低蓝光、夜间模式等功能至关重要。
* **文件管理：** 强大的个人图书馆管理功能是核心需求，包括自动检测本地书籍、分类、排序、搜索和书签同步。
* **学习与批注：** 用户需要方便地进行划词、高亮、做笔记和添加书签，并且能够将这些笔记导出。
* **跨设备同步：** 许多成功的阅读器都提供了跨设备同步功能，让用户可以在不同设备上无缝切换阅读。

### **竞品分析**
我搜索了市面上主要的桌面电子书阅读器，发现以下几点：

* **PDF 阅读器占据主流：** 许多桌面应用，如 Adobe Acrobat Reader 和 iLovePDF，主要专注于 PDF 文档的阅读和处理，功能更偏向于办公，而非纯粹的电子书阅读。
* **功能单一：** 大部分免费的桌面阅读器功能相对基础，主要集中在打开和阅读文件，缺乏高级的书架管理、笔记和学习功能。
* **专注于移动端：** 许多知名的电子书应用（如 ReadEra）主要在移动端（Google Play）上发展，桌面版本相对较少或功能受限。
* **部分应用提供增值服务：** 一些应用通过提供高级功能（如文件转换、主题设置、打印）来吸引用户升级到付费版本。

### **Yu Reader 的优势和定位**

基于以上分析，Yu Reader 的定位非常清晰，并具有潜在的竞争优势：
* **纯桌面端开发模式：** 专注于桌面端用户，可以提供更稳定、更强大的本地性能和功能，避免了依赖云服务带来的潜在问题。
* **一体化的学习服务：** “学习服务模块”是其主要亮点。如果能实现划词翻译、生词本、阅读统计等功能，将使其在众多基础阅读器中脱颖而出。
* **现代化的技术栈：** 采用 Electron + Vue3 + Element Plus，能够轻松构建出界面美观、用户体验友好的应用。
* **明确的功能规划：** 项目的五大核心功能模块规划完整，覆盖了用户从书架管理到个性化阅读、再到学习与记录的全链路需求。

----------
**“我的书架模块”** 进行详细的需求分析。

根据项目的整体目标，我的书架模块不仅是简单的文件列表，更是用户管理和享受阅读体验的核心入口。以下是该模块的详细需求定义，涵盖了书籍导入、分类逻辑和元数据管理三个核心方面。

### **一、 书籍导入流程**

用户应该能方便地将电子书文件添加到应用中。我们应支持多种导入方式，并确保过程流畅。

#### **1. 手动导入**
* **拖放 (Drag and Drop)**：用户可以直接将本地文件（或文件夹）拖放到应用窗口中，系统自动识别并导入支持的格式文件。
* **文件选择 (File Picker)**：提供一个“添加书籍”按钮，打开系统文件选择器，用户可以浏览并选择一个或多个电子书文件进行导入。
* **文件夹导入**：用户可以选择一个包含多个电子书文件的文件夹，系统将遍历该文件夹及其子文件夹，自动导入所有支持的电子书文件。

#### **2. 格式支持**
* **核心格式**：必须支持主流的电子书格式，如 `.epub`、`.mobi`、`.azw3`。
* **通用格式**：同时支持 `.pdf`、`.txt`、`.docx` 等文档格式，以满足用户阅读更多类型文档的需求。
* **压缩包支持**：为方便漫画或系列书籍的管理，可考虑支持 `.zip`、`.rar` 等压缩包格式，并自动解压或读取内容。

#### **3. 导入后的处理**
* **文件复制**：导入时，应用应将电子书文件复制到应用指定的本地存储目录中，以确保文件管理的一致性和可靠性，避免因原文件被移动或删除而导致应用出错。
* **数据索引**：导入成功后，立即解析书籍的元数据（如标题、作者、封面等），并将其存储到 SQLite3 数据库中，以供快速检索和展示。
* **导入状态反馈**：提供清晰的导入进度条和完成通知，尤其是在导入大量文件时，让用户了解当前状态。

### **二、 书籍分类和管理逻辑**

提供灵活的书籍组织方式，帮助用户高效地管理庞大的书库。

#### **1. 自动分类**
* **按元数据分类**：系统应能根据书籍的元数据（如作者、出版社、系列等）自动创建或归类到相应的虚拟分组中。
* **按格式分类**：自动将不同格式的书籍（例如：`EPUB`、`PDF`）进行区分。

#### **2. 手动分类**
* **自定义标签 (Tags)**：用户可以为每本书添加一个或多个自定义标签（例如：“科幻”、“历史”、“未读”）。
* **创建书单 (Collections)**：用户可以创建自定义的书单（例如：“我的最爱”、“待读列表”），并手动将书籍添加到这些书单中。
* **星标收藏**：提供“星标”功能，让用户快速标记和找到重要的书籍。

#### **3. 筛选与排序**
* **筛选器**：用户可以根据标签、作者、格式、阅读状态等条件筛选书籍。
* **多种排序方式**：支持按导入时间、标题、作者、阅读进度等多种方式进行排序。

### **三、 书籍元数据管理**

元数据是书架模块的核心，它决定了书籍的展示和管理效果。

#### **1. 自动获取**
* **从文件解析**：系统应优先从电子书文件内部（如 EPUB 文件的 `content.opf`）解析出标题、作者、封面图片、出版商、描述等信息。
* **本地文件信息**：如果文件中缺少元数据，则使用文件名作为默认标题。

#### **2. 手动编辑**
* **元数据编辑界面**：提供一个界面，允许用户手动编辑或补充书籍的元数据，包括标题、作者、封面、简介、系列等。
* **封面更换**：允许用户自定义书籍封面，可以从本地选择图片文件进行替换。

#### **3. 展示方式**
* **封面视图 (Grid View)**：以网格布局展示书籍封面，提供沉浸式的视觉体验。
* **列表视图 (List View)**：以列表形式展示书籍，包含标题、作者、阅读进度等关键信息，适合快速浏览。
* **阅读进度条**：在每本书的封面或列表项下方，展示一个可视化的进度条，让用户一目了然地知道阅读进展。

-----------

**“学习服务模块 (Learning)”** 进行详细的需求分析。

学习服务模块是 Yu Reader 的一个核心亮点，旨在将阅读体验与学习过程相结合，帮助用户更深入地理解和吸收书籍内容。以下是该模块的详细需求定义。

### **一、 划词服务**

划词是学习服务的基础，它应提供多种功能，而不仅仅是简单的文本选择。

* **划线高亮 (Highlight)**：用户可以选择文本并进行高亮，支持多种颜色标记，以便快速识别重点内容。
* **笔记记录 (Annotation)**：用户在划选文本后，可以添加相关的文字笔记。这些笔记应与原文内容关联，并在阅读时可见。
* **划词翻译**：
    * **数据源**：集成高质量的翻译服务 API（例如 Google Translate, DeepL 等），实现划词即时翻译。
    * **交互**：用户划选文本后，可以快速查看翻译结果，并选择是否将原文和译文保存到笔记中。
* **词典查询**：
    * **数据源**：集成在线或离线词典服务，用户划选单词后，可以立即查询其释义、发音、例句等。
    * **交互**：提供一个简洁的弹窗或侧边栏，展示查询结果，方便用户快速理解生词。

### **二、 生词本**

生词本是用户长期积累和复习学习成果的核心功能。

* **自动添加**：用户在进行词典查询或划词翻译时，可以一键将生词添加到生词本中。
* **生词本管理**：
    * **列表展示**：生词本应以列表形式清晰展示所有生词，包含单词、释义、例句和添加时间。
    * **分组/标签**：允许用户对生词进行自定义分组或添加标签，例如：“第一章生词”、“高中词汇”。
* **复习功能**：
    * **卡片式复习**：提供类似闪卡（Flashcard）的复习模式，帮助用户记忆生词。
    * **自定义复习计划**：用户可以设置复习频率和复习内容，例如：每天复习 20 个生词。

### **三、 阅读统计与数据分析**

通过数据化的方式，帮助用户量化自己的阅读行为和学习成果。

* **阅读时长统计**：
    * **实时统计**：应用应在用户打开书籍时开始计时，并在关闭时停止。
    * **数据展示**：以图表形式展示用户的每日、每周、每月阅读时长，让用户直观了解自己的阅读习惯。
* **阅读速度统计**：
    * **WPM (Words Per Minute)**：计算并展示用户的阅读速度，用户可以以此为基准，逐步提升阅读效率。
* **生词统计**：
    * **高频词汇**：统计用户在某本书中查询或添加到生词本的生词数量。
    * **阅读难度分析**：根据生词频率或生词本中的单词数量，为书籍标注“阅读难度”，帮助用户选择合适的书籍。
* **成就系统 (Gamification)**：
    * **阅读勋章**：根据阅读时长、阅读天数、完成的书籍数量等，授予用户虚拟勋章。
    * **挑战目标**：用户可以设置自己的阅读目标（例如：本月读完三本书），并追踪进度，增加阅读的动力。

### **四、 数据存储与同步**

为了确保用户学习成果的持久性，需要可靠的数据存储和同步方案。

* **本地数据库**：所有学习数据（划线、笔记、生词本、统计数据）都应可靠地存储在本地的 SQLite3 数据库中。
* **同步方案**：由于项目定位为纯桌面应用，可以考虑提供手动备份/恢复功能，或者在未来版本中通过云服务实现跨设备同步。

-----------


 **“书签笔记模块 (Bookmark)”** 进行详细的需求分析。

书签笔记模块与学习服务模块紧密相关，但它更侧重于对阅读进度的标记和对阅读内容的摘录与整理。以下是该模块的详细需求定义。

### **一、 书签功能**

书签是用户快速定位到特定阅读位置的关键工具。

* **添加书签**：
    * **一键添加**：用户应能在任何阅读页面通过一个简单的操作（例如，点击一个按钮或使用快捷键）来添加书签。
    * **书签名称**：添加书签时，系统应自动记录当前页码、时间，并允许用户自定义书签的名称或备注。
* **书签列表**：
    * **集中管理**：提供一个统一的书签列表界面，展示所有书籍的书签。
    * **按书籍分组**：书签应按所属书籍进行分组，方便用户快速找到特定书籍的书签。
    * **信息展示**：书签列表中应清晰展示书签名称、页码、创建时间、以及书签所在的文本摘要。
* **跳转与删除**：
    * **快速跳转**：用户点击书签列表中的任意项，即可立即跳转到对应的阅读位置。
    * **编辑与删除**：允许用户编辑书签的名称或备注，以及删除不需要的书签。

### **二、 笔记功能**

笔记功能是用户整理思路、记录灵感的重要手段，它与划线高亮功能相辅相成。

* **创建笔记**：
    * **基于文本**：用户可以对划选的文本片段创建笔记。这部分需求与学习服务模块的“笔记记录”功能重叠，但这里更强调其独立的管理和导出能力。
    * **独立笔记**：用户可以在不划选文本的情况下，在任意阅读页面创建独立的笔记，用于记录对整个页面的想法或感悟。
* **笔记管理**：
    * **笔记列表**：提供一个界面，集中展示所有笔记。
    * **信息展示**：笔记列表中应包含笔记内容、关联的文本、所属书籍、创建时间等信息。
    * **筛选与搜索**：支持根据关键词搜索笔记，或根据所属书籍、标签进行筛选。
* **笔记导出**：
    * **格式支持**：提供多种导出格式，如纯文本 (`.txt`)、Markdown (`.md`) 或 PDF，方便用户将笔记用于其他用途。
    * **导出内容**：导出的内容应包括原文引用、笔记内容、页码和书籍信息，确保内容的完整性和可追溯性。

### **三、 划线高亮管理**

划线高亮是视觉化的笔记方式，需要提供便捷的管理功能。

* **高亮列表**：
    * **集中展示**：提供一个列表，展示所有高亮的文本片段。
    * **按颜色或书籍分组**：允许用户按高亮颜色或所属书籍对列表进行分组。
* **编辑与导出**：
    * **颜色修改**：用户可以更改高亮部分的颜色。
    * **删除高亮**：用户可以移除高亮标记。
    * **导出高亮**：提供导出功能，将所有高亮文本导出为纯文本或 Markdown 格式。

### **四、 数据存储与同步**

与学习服务模块类似，书签和笔记数据也需要安全存储。

* **本地数据库**：所有书签、笔记和高亮信息都应存储在本地的 SQLite3 数据库中，与书籍文件进行关联。
* **跨设备同步**：在未来版本中，可以考虑通过云服务实现书签和笔记的跨设备同步，确保用户在不同设备上的阅读体验一致。


-----------------

**“设置模块 (Settings)”** 进行详细的需求分析。

设置模块是应用个性化和功能定制的核心，它允许用户根据自己的偏好调整应用的行为和外观，以获得最佳的使用体验。以下是该模块的详细需求定义。

### **一、 阅读设置**

这部分设置直接影响用户的阅读体验，是应用的核心配置。

* **字体设置**：
    * **字体选择**：提供多种内置字体供用户选择，并支持用户导入自定义字体。
    * **字号调整**：允许用户自由调整字体大小，并提供预设的字号选项。
    * **行高与字间距**：支持调整行高和字间距，以优化文本的排版效果。
* **背景与主题**：
    * **预设主题**：提供多种预设主题，如日间模式（白色背景）、夜间模式（黑色背景）、护眼模式（米黄色背景）。
    * **自定义背景**：允许用户自定义背景颜色或导入背景图片。
* **翻页模式**：
    * **翻页动画**：提供多种翻页动画效果，如平滑滚动、左右滑动、翻页模拟等。
    * **翻页方式**：支持通过点击屏幕左右两侧、键盘方向键或鼠标滚轮进行翻页。
* **阅读排版**：
    * **对齐方式**：支持左对齐、居中对齐、两端对齐等。
    * **页边距**：允许用户调整阅读界面的页边距。

### **二、 功能设置**

这部分设置主要用于控制应用的行为和功能。

* **文件管理**：
    * **默认存储路径**：允许用户自定义电子书文件的默认存储路径。
    * **自动导入**：提供选项，让应用在启动时自动扫描指定文件夹，导入新的电子书。
* **快捷键设置**：
    * **自定义快捷键**：提供一个界面，让用户可以自定义常用的操作（如翻页、添加书签、打开笔记面板等）的快捷键。
    * **预设快捷键方案**：提供一套默认的快捷键方案，并支持一键恢复。
* **集成服务**：
    * **翻译服务**：允许用户选择或配置不同的翻译服务 API，如 Google Translate, DeepL 等，以用于划词翻译。
    * **词典服务**：允许用户选择或配置不同的词典服务。
* **启动设置**：
    * **开机自启**：提供选项，让应用在系统启动时自动运行。
    * **启动默认界面**：用户可以设置应用启动时默认进入的界面（如书架、上次阅读的书籍等）。

### **三、 账户与隐私**

这部分设置主要用于管理用户账户和数据隐私。

* **账户管理**：
    * **登录/注销**：提供登录和注销账户的入口。
    * **个人信息**：用户可以查看和编辑自己的基本信息。
* **数据备份与恢复**：
    * **本地备份**：提供手动备份功能，将用户的书签、笔记、生词本等数据导出为文件。
    * **本地恢复**：支持从备份文件中恢复数据。
* **隐私保护**：
    * **阅读记录清除**：允许用户清除所有阅读记录和统计数据。
    * **数据同步设置**：如果未来支持云同步，这里将提供同步开关和数据管理选项。


------------


 **“个人中心模块 (Profile)”** 进行详细的需求分析。

个人中心模块是用户在应用中的身份象征，它集中展示了用户的个人信息、阅读成就和数据统计，旨在为用户提供归属感和成就感。以下是该模块的详细需求定义。

### **一、 用户信息展示**

这部分主要用于展示用户的身份信息。

* **用户头像与昵称**：
    * **头像**：支持用户上传自定义头像。
    * **昵称**：展示用户的昵称，并提供修改功能。
* **账户信息**：
    * **邮箱/手机号**：展示用于注册/登录的邮箱或手机号。
    * **会员状态**：如果未来有会员功能，这里将展示用户的会员等级或到期时间。
* **签名/个人简介**：
    * 提供一个文本框，让用户可以编辑自己的个性签名或个人简介。

### **二、 阅读数据统计**

这部分是个人中心的核心，将用户的阅读行为数据化、可视化。

* **核心数据总览**：
    * **阅读总时长**：展示用户自注册以来积累的总阅读时长。
    * **总阅读天数**：展示用户累计阅读的天数。
    * **已读书籍数量**：展示用户已完成阅读的书籍总数。
    * **生词本数量**：展示用户生词本中的生词总数。
* **数据可视化**：
    * **阅读时长趋势图**：以柱状图或折线图的形式，展示用户每日、每周或每月的阅读时长，帮助用户了解自己的阅读习惯。
    * **阅读偏好分析**：根据用户阅读的书籍类型、标签，生成饼图或词云，展示用户的阅读偏好。
* **年度报告/月度总结**：
    * 提供一个功能，可以自动生成用户的年度阅读报告或月度总结，回顾阅读成就和习惯。

### **三、 成就系统 (Achievements)**

通过游戏化的方式，激励用户持续阅读。

* **勋章墙**：
    * **成就勋章**：根据用户的阅读行为，解锁不同的虚拟勋章，例如：“初读入门”（完成第一本书）、“阅读达人”（累计阅读时长超过100小时）、“夜读之星”（在夜间模式下阅读超过10小时）等。
    * **勋章展示**：提供一个“勋章墙”界面，展示用户已经获得的和待解锁的所有勋章。
* **排行榜**：
    * **阅读时长排行榜**：如果未来支持云同步，可以考虑实现用户间的阅读时长排行榜，增加互动性和竞争性。

### **四、 数据管理**

这部分提供数据相关的操作入口，确保用户对自己的数据拥有完全控制权。

* **数据备份与恢复**：
    * **一键备份**：提供一个按钮，让用户可以一键备份所有本地数据（书签、笔记、生词本、阅读记录等）。
    * **数据恢复**：允许用户从备份文件中恢复数据。
* **数据清除**：
    * **清除阅读记录**：提供选项，让用户可以选择性地清除阅读记录或所有数据。

---

至此，我们已经完成了对《Yu Reader》项目的五个核心功能模块的详细需求分析：
1. 我的书架模块 (Bookshelf)
2. 学习服务模块 (Learning)
3. 书签笔记模块 (Bookmark)
4. 设置模块 (Settings)
5. 个人中心模块 (Profile)

