好的，我已切换到 **Product Manager (John)** 模式。

根据您提供的项目概述、目标和我们之前对五个核心功能模块（我的书架、学习服务、书签笔记、设置、个人中心）的详细分析，我已为您撰写了一份详细的《Yu Reader》产品需求文档（PRD）。

这份 PRD 旨在为开发团队提供一个清晰、全面的指导，确保所有成员对项目的目标、功能和需求有一个共同的理解。

---

### **产品需求文档 (PRD)**

#### **项目名称：** Yu Reader (彧阅读器)

#### **版本号：** V1.0

#### **文档作者：** 产品经理 John

#### **编写日期：** 2025年8月1日

---

### **1. 项目概述**

Yu Reader 是一个现代化的、基于桌面端的跨平台电子书阅读器应用。项目旨在提供一个纯粹、高效、集成的阅读和学习环境，利用现代技术栈（Electron + Vue3 + Element Plus + SQLite3）为 Windows 和 macOS 用户提供卓越的体验。本应用通过整合书架管理、深度学习工具和个性化设置，力求在基础阅读功能之上，为用户创造更大的价值。

### **2. 项目目标**

* 开发一个功能全面、用户体验友好的桌面电子书阅读器。
* 实现跨平台兼容性，初步支持 Windows 和 macOS。
* 通过提供独特的学习服务和数据统计功能，建立差异化竞争优势。
* 利用 AI 助手协作开发模式，实现高效、智能的开发流程。

### **3. 目标用户**

* **学生与研究人员**：需要一个功能强大的工具来阅读教材、论文，并进行划线、笔记和生词查询。
* **深度阅读爱好者**：寻求一个纯净、无干扰的阅读环境，并希望对自己的阅读进度和习惯进行管理。
* **知识工作者**：需要管理和阅读大量文档，并希望有一个工具能够帮助他们整理和导出阅读笔记。

### **4. 核心功能模块**

本应用包含五大核心功能模块，每个模块的具体需求如下：

#### **4.1. 我的书架模块 (Bookshelf)**

* **功能需求**：
    * **书籍导入**：支持拖放、文件选择和文件夹导入，以导入多种格式（.epub, .mobi, .azw3, .pdf, .txt, .docx, .zip）的电子书。
    * **文件处理**：导入时将文件复制到应用指定目录，并解析元数据。
    * **书库管理**：支持按作者、系列、标签等自动和手动分类，提供自定义书单、星标收藏功能。
    * **展示与排序**：支持封面视图和列表视图，提供按导入时间、标题、作者、阅读进度等多种排序方式。
    * **元数据编辑**：允许用户手动编辑书籍的标题、作者、封面等元数据。

#### **4.2. 学习服务模块 (Learning)**

* **功能需求**：
    * **划词功能**：支持划线高亮（多种颜色）、添加笔记、实时翻译和词典查询。
    * **生词本**：支持一键将生词添加到生词本，并提供生词管理、分组和复习功能。
    * **阅读统计**：自动统计阅读时长、阅读速度（WPM）、生词数量，并以图表形式展示数据。
    * **成就系统**：根据用户的阅读行为，解锁虚拟勋章，增加阅读的趣味性。

#### **4.3. 书签笔记模块 (Bookmark)**

* **功能需求**：
    * **书签功能**：支持一键添加书签，并记录页码和时间，允许用户自定义书签名称，提供书签列表和快速跳转功能。
    * **笔记功能**：支持对划选文本和独立页面创建笔记，并提供笔记列表、搜索、筛选和多种格式（.txt, .md, .pdf）的导出功能。
    * **高亮管理**：提供高亮文本列表，支持更改高亮颜色、删除高亮和导出高亮内容。

#### **4.4. 设置模块 (Settings)**

* **功能需求**：
    * **阅读设置**：提供字体（字号、行高）、背景（主题、自定义颜色）、翻页模式和阅读排版等个性化配置选项。
    * **功能设置**：允许用户自定义默认存储路径、快捷键，并配置翻译/词典服务。
    * **账户与隐私**：提供登录/注销入口，并支持本地数据备份与恢复，以及清除阅读记录等隐私管理功能。

#### **4.5. 个人中心模块 (Profile)**

* **功能需求**：
    * **用户信息**：展示用户头像、昵称、账户信息，并支持修改。
    * **数据统计**：展示阅读总时长、总阅读天数、已读书籍数量等核心数据，并以图表形式展示阅读趋势和偏好。
    * **成就系统**：集中展示用户已获得的和待解锁的成就勋章。
    * **数据管理**：提供数据备份、恢复和清除的入口。

### **5. 技术栈**

* **前端框架**：Vue3
* **UI 组件库**：Element Plus
* **跨平台框架**：Electron
* **本地数据库**：SQLite3

### **6. 未来版本考虑**

* **云同步功能**：通过云服务实现书架、笔记、书签和阅读进度的跨设备同步。
* **多端适配**：考虑开发移动端或 Web 端应用，与桌面端数据互通。
* **更多格式支持**：增加对漫画（CBR/CBZ）和有声书格式的支持。
* **社区功能**：允许用户分享笔记和书单，形成阅读社区。

这份 PRD 为 Yu Reader 项目的开发工作奠定了坚实的基础。接下来，您可以继续使用其他代理模式，例如让 **Architect (Winston)** 基于此 PRD 进行系统架构设计。